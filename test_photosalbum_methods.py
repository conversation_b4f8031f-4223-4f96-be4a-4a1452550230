#!/usr/bin/env python3
"""
检查PhotosAlbum类的可用方法
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src-python', 'python'))

def check_photosalbum_methods():
    """检查PhotosAlbum类的所有可用方法"""
    print("🔍 检查PhotosAlbum类的可用方法...")
    
    try:
        from osxphotos.photosalbum import PhotosAlbum
        
        # 创建PhotosAlbum实例
        album = PhotosAlbum("marked delete")
        
        # 获取所有方法和属性
        all_attrs = dir(album)
        
        # 过滤出公共方法
        public_methods = [attr for attr in all_attrs if not attr.startswith('_')]
        
        print(f"📋 PhotosAlbum公共方法: {public_methods}")
        
        # 检查每个方法的类型
        print("\n📝 方法详情:")
        for method_name in public_methods:
            method = getattr(album, method_name)
            method_type = type(method).__name__
            print(f"   {method_name}: {method_type}")
            
            # 如果是方法，尝试获取文档字符串
            if callable(method):
                doc = method.__doc__
                if doc:
                    # 只显示第一行文档
                    first_line = doc.split('\n')[0].strip()
                    print(f"      -> {first_line}")
        
        # 特别检查移除相关的方法
        print("\n🔍 查找移除相关的方法:")
        remove_methods = [attr for attr in all_attrs if 'remove' in attr.lower() or 'delete' in attr.lower() or 'clear' in attr.lower()]
        if remove_methods:
            print(f"   找到移除相关方法: {remove_methods}")
        else:
            print("   ❌ 没有找到移除相关的方法")
            
        # 检查是否有类似list的方法
        print("\n🔍 查找类似list的方法:")
        list_methods = [attr for attr in all_attrs if attr in ['pop', 'remove', 'clear', 'discard']]
        if list_methods:
            print(f"   找到类似list的方法: {list_methods}")
        else:
            print("   ❌ 没有找到类似list的方法")
            
        return True
        
    except Exception as e:
        print(f"❌ 检查PhotosAlbum方法失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_alternative_removal():
    """测试替代的移除方法"""
    print("\n🧪 测试替代的移除方法...")
    
    try:
        import osxphotos
        from osxphotos.photosalbum import PhotosAlbum
        
        # 获取测试照片
        photosdb = osxphotos.PhotosDB()
        photos_list = photosdb.photos()
        test_uuid = "DDDCEB2B-B8BA-4FEA-B95A-B08BDF31D469"
        
        target_photo = None
        for photo in photos_list:
            if photo.uuid == test_uuid:
                target_photo = photo
                break
                
        if not target_photo:
            print("❌ 未找到测试照片")
            return False
            
        album = PhotosAlbum("marked delete")
        
        # 方法1：尝试重新创建相册（清空）
        print("📝 方法1：尝试重新创建相册...")
        try:
            # 获取当前相册中的所有照片
            current_photos = album.photos()
            print(f"   当前相册中有 {len(current_photos)} 张照片")
            
            # 创建一个新的相册实例，看看是否会清空
            new_album = PhotosAlbum("marked delete test")
            print("   ✅ 创建新相册成功")
            
        except Exception as e:
            print(f"   ❌ 方法1失败: {e}")
            
        # 方法2：检查是否有clear方法
        print("\n📝 方法2：检查clear方法...")
        if hasattr(album, 'clear'):
            try:
                album.clear()
                print("   ✅ clear方法成功")
            except Exception as e:
                print(f"   ❌ clear方法失败: {e}")
        else:
            print("   ❌ 没有clear方法")
            
        # 方法3：检查是否可以设置为空列表
        print("\n📝 方法3：尝试设置为空...")
        try:
            # 尝试不同的方式
            if hasattr(album, 'update'):
                album.update([])
                print("   ✅ update([])方法成功")
            else:
                print("   ❌ 没有update方法")
        except Exception as e:
            print(f"   ❌ 方法3失败: {e}")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试替代移除方法失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 启动PhotosAlbum方法检查")
    print("="*50)
    
    # 检查可用方法
    methods_result = check_photosalbum_methods()
    
    # 测试替代移除方法
    removal_result = test_alternative_removal()
    
    print("\n" + "="*50)
    print("📋 检查摘要")
    print("="*50)
    print(f"方法检查: {'✅ 成功' if methods_result else '❌ 失败'}")
    print(f"移除方法测试: {'✅ 成功' if removal_result else '❌ 失败'}")
    
    print("\n💡 建议:")
    print("   1. 如果没有remove方法，可能需要使用其他方式")
    print("   2. 考虑重新创建相册来清空内容")
    print("   3. 或者只在数据库中标记，不从相册移除")
    
    print("="*50)
    
    return methods_result

if __name__ == "__main__":
    result = main()
    sys.exit(0 if result else 1)
