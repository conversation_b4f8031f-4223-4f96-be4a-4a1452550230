#!/usr/bin/env python3
"""
调试照片移除功能
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src-python', 'python'))

def debug_removal():
    """调试照片移除功能"""
    print("🔍 调试照片移除功能...")
    
    try:
        import osxphotos
        from osxphotos.photosalbum import PhotosAlbum
        
        # 测试照片UUID
        test_uuid = "DDDCEB2B-B8BA-4FEA-B95A-B08BDF31D469"
        
        # 获取PhotosDB和照片
        photosdb = osxphotos.PhotosDB()
        photos_list = photosdb.photos()
        
        target_photo = None
        for photo in photos_list:
            if photo.uuid == test_uuid:
                target_photo = photo
                break
                
        if not target_photo:
            print("❌ 未找到测试照片")
            return False
            
        print(f"✅ 找到测试照片: {target_photo.filename}")
        
        # 创建相册
        album = PhotosAlbum("marked delete")
        
        # 1. 检查当前相册状态
        print("\n1️⃣ 检查当前相册状态...")
        current_photos = album.photos()
        print(f"   相册中当前照片数: {len(current_photos)}")
        
        # 显示相册中所有照片的UUID
        current_uuids = [p.uuid for p in current_photos]
        print(f"   相册中的照片UUID: {current_uuids}")
        
        # 检查测试照片是否在相册中
        test_in_album = test_uuid in current_uuids
        print(f"   测试照片是否在相册中: {test_in_album}")
        
        # 2. 如果测试照片不在相册中，先添加它
        if not test_in_album:
            print("\n2️⃣ 添加测试照片到相册...")
            album.append(target_photo)
            
            # 重新检查
            updated_photos = album.photos()
            updated_uuids = [p.uuid for p in updated_photos]
            print(f"   添加后相册中照片数: {len(updated_photos)}")
            print(f"   添加后相册中的照片UUID: {updated_uuids}")
            
            if test_uuid in updated_uuids:
                print("   ✅ 测试照片成功添加到相册")
            else:
                print("   ❌ 测试照片添加失败")
                return False
        
        # 3. 尝试移除照片
        print("\n3️⃣ 尝试移除照片...")
        
        # 获取当前相册中的所有照片
        before_removal = album.photos()
        before_uuids = [p.uuid for p in before_removal]
        print(f"   移除前相册中照片数: {len(before_removal)}")
        print(f"   移除前照片UUID: {before_uuids}")
        
        # 创建新的照片列表，排除要移除的照片
        remaining_photos = [p for p in before_removal if p.uuid != test_uuid]
        print(f"   应该保留的照片数: {len(remaining_photos)}")
        print(f"   应该保留的照片UUID: {[p.uuid for p in remaining_photos]}")
        
        # 使用update方法重新设置相册内容
        print("   执行 album.update(remaining_photos)...")
        album.update(remaining_photos)
        print("   ✅ update操作完成")
        
        # 4. 验证移除结果
        print("\n4️⃣ 验证移除结果...")
        
        # 重新获取相册照片
        after_removal = album.photos()
        after_uuids = [p.uuid for p in after_removal]
        print(f"   移除后相册中照片数: {len(after_removal)}")
        print(f"   移除后照片UUID: {after_uuids}")
        
        # 检查测试照片是否还在相册中
        still_in_album = test_uuid in after_uuids
        print(f"   测试照片是否仍在相册中: {still_in_album}")
        
        if not still_in_album:
            print("   ✅ 照片移除成功！")
            return True
        else:
            print("   ❌ 照片移除失败")
            
            # 5. 进一步调试
            print("\n5️⃣ 进一步调试...")
            
            # 检查是否有重复的照片
            uuid_counts = {}
            for uuid in after_uuids:
                uuid_counts[uuid] = uuid_counts.get(uuid, 0) + 1
                
            duplicates = {uuid: count for uuid, count in uuid_counts.items() if count > 1}
            if duplicates:
                print(f"   发现重复照片: {duplicates}")
            else:
                print("   没有发现重复照片")
                
            # 尝试清空相册
            print("   尝试清空相册...")
            album.update([])
            
            empty_check = album.photos()
            print(f"   清空后相册中照片数: {len(empty_check)}")
            
            if len(empty_check) == 0:
                print("   ✅ 相册清空成功")
            else:
                print("   ❌ 相册清空失败")
                
            return False
        
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 启动照片移除功能调试")
    print("="*50)
    
    result = debug_removal()
    
    print("\n" + "="*50)
    print("📋 调试摘要")
    print("="*50)
    print(f"移除功能调试: {'✅ 成功' if result else '❌ 失败'}")
    
    if not result:
        print("\n💡 可能的解决方案:")
        print("   1. PhotosAlbum.update()方法可能不按预期工作")
        print("   2. 相册中可能有重复的照片")
        print("   3. 可能需要使用其他方法来移除照片")
        print("   4. 考虑只在数据库中标记，不从相册移除")
    
    print("="*50)
    
    return result

if __name__ == "__main__":
    result = main()
    sys.exit(0 if result else 1)
