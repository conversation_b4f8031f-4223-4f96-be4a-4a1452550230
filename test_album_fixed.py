#!/usr/bin/env python3
"""
测试修复后的相册管理功能
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src-python', 'python'))

def test_album_manager_fixed():
    """测试修复后的相册管理器"""
    print("🧪 测试修复后的相册管理器...")
    
    try:
        from photo_dedup.album_manager import AlbumManager
        
        # 创建相册管理器
        manager = AlbumManager()
        print("✅ 相册管理器创建成功")
        
        # 测试照片UUID
        test_uuid = "DDDCEB2B-B8BA-4FEA-B95A-B08BDF31D469"
        
        # 1. 测试确保相册存在
        print("\n1️⃣ 测试确保相册存在...")
        try:
            result = manager.ensure_album_exists()
            print(f"   确保相册存在: {'✅ 成功' if result else '❌ 失败'}")
        except Exception as e:
            print(f"   ❌ 确保相册存在失败: {e}")
            
        # 2. 测试添加照片到相册
        print("\n2️⃣ 测试添加照片到相册...")
        try:
            result = manager.add_photo_to_album(test_uuid)
            print(f"   添加照片结果: {'✅ 成功' if result else '❌ 失败'}")
        except Exception as e:
            print(f"   ❌ 添加照片失败: {e}")
            
        # 3. 测试获取相册中的照片
        print("\n3️⃣ 测试获取相册中的照片...")
        try:
            photos = manager.get_album_photos()
            print(f"   相册中照片数量: {len(photos)}")
            if test_uuid in photos:
                print(f"   ✅ 测试照片 {test_uuid} 在相册中")
            else:
                print(f"   ⚠️ 测试照片 {test_uuid} 不在相册中")
                print(f"   📋 相册中的照片: {photos[:5]}...")  # 显示前5个
        except Exception as e:
            print(f"   ❌ 获取相册照片失败: {e}")
            
        # 4. 测试从相册移除照片
        print("\n4️⃣ 测试从相册移除照片...")
        try:
            result = manager.remove_photo_from_album(test_uuid)
            print(f"   移除照片结果: {'✅ 成功' if result else '❌ 失败'}")
        except Exception as e:
            print(f"   ❌ 移除照片失败: {e}")
            
        # 5. 验证照片已被移除
        print("\n5️⃣ 验证照片已被移除...")
        try:
            photos_after = manager.get_album_photos()
            print(f"   移除后相册中照片数量: {len(photos_after)}")
            if test_uuid not in photos_after:
                print(f"   ✅ 测试照片 {test_uuid} 已从相册中移除")
            else:
                print(f"   ⚠️ 测试照片 {test_uuid} 仍在相册中")
        except Exception as e:
            print(f"   ❌ 验证移除失败: {e}")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_with_collector():
    """测试与collector模块的集成"""
    print("\n🧪 测试与collector模块的集成...")
    
    try:
        from photo_dedup.collector import mark_photo_for_deletion, unmark_photo_for_deletion, get_photo_mark_status
        
        test_uuid = "DDDCEB2B-B8BA-4FEA-B95A-B08BDF31D469"
        
        # 1. 检查初始状态
        print("\n1️⃣ 检查照片初始状态...")
        initial_status = get_photo_mark_status(test_uuid)
        print(f"   初始标记状态: {initial_status}")
        
        # 2. 标记照片删除
        print("\n2️⃣ 标记照片删除...")
        mark_result = mark_photo_for_deletion(test_uuid)
        print(f"   标记结果: {'✅ 成功' if mark_result else '❌ 失败'}")
        
        if mark_result:
            # 3. 验证标记状态
            print("\n3️⃣ 验证标记状态...")
            marked_status = get_photo_mark_status(test_uuid)
            print(f"   标记后状态: {marked_status}")
            
            # 4. 检查相册中是否有照片
            print("\n4️⃣ 检查相册中是否有照片...")
            from photo_dedup.album_manager import get_marked_delete_album_photos
            album_photos = get_marked_delete_album_photos()
            print(f"   相册中照片数量: {len(album_photos)}")
            if test_uuid in album_photos:
                print(f"   ✅ 照片 {test_uuid} 在相册中")
            else:
                print(f"   ⚠️ 照片 {test_uuid} 不在相册中")
                
            # 5. 取消标记
            print("\n5️⃣ 取消照片标记...")
            unmark_result = unmark_photo_for_deletion(test_uuid)
            print(f"   取消标记结果: {'✅ 成功' if unmark_result else '❌ 失败'}")
            
            if unmark_result:
                # 6. 验证取消标记状态
                print("\n6️⃣ 验证取消标记状态...")
                final_status = get_photo_mark_status(test_uuid)
                print(f"   最终状态: {final_status}")
                
                # 7. 检查相册中照片是否被移除
                print("\n7️⃣ 检查相册中照片是否被移除...")
                final_album_photos = get_marked_delete_album_photos()
                print(f"   最终相册中照片数量: {len(final_album_photos)}")
                if test_uuid not in final_album_photos:
                    print(f"   ✅ 照片 {test_uuid} 已从相册中移除")
                else:
                    print(f"   ⚠️ 照片 {test_uuid} 仍在相册中")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 启动修复后的相册管理功能测试")
    print("="*60)
    
    # 测试相册管理器
    manager_result = test_album_manager_fixed()
    
    # 测试集成功能
    integration_result = test_integration_with_collector()
    
    print("\n" + "="*60)
    print("📋 测试摘要")
    print("="*60)
    print(f"相册管理器测试: {'✅ 通过' if manager_result else '❌ 失败'}")
    print(f"集成功能测试: {'✅ 通过' if integration_result else '❌ 失败'}")
    
    if manager_result and integration_result:
        print("\n🎉 所有测试通过！相册管理功能修复成功。")
        print("\n💡 修复要点:")
        print("   1. ✅ 使用PhotoInfo对象而不是UUID字符串")
        print("   2. ✅ 使用append()方法添加单张照片")
        print("   3. ✅ 使用photos()方法获取相册照片列表")
        print("   4. ✅ 相册会自动创建如果不存在")
    else:
        print("\n❌ 部分测试失败，需要进一步检查。")
    
    print("="*60)
    
    return manager_result and integration_result

if __name__ == "__main__":
    result = main()
    sys.exit(0 if result else 1)
