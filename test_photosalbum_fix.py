#!/usr/bin/env python3
"""
测试PhotosAlbum API修复
"""

import sys
import os
from unittest.mock import patch, MagicMock

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src-python', 'python'))


def test_photosalbum_initialization():
    """测试PhotosAlbum初始化修复"""
    print("🧪 测试PhotosAlbum初始化修复...")
    
    try:
        from photo_dedup.album_manager import AlbumManager
        
        # 模拟PhotosAlbum类，要求name参数
        with patch('osxphotos.photosalbum.PhotosAlbum') as mock_photos_album_class:
            # 创建模拟的PhotosAlbum实例
            mock_photos_album = MagicMock()
            mock_photos_album_class.return_value = mock_photos_album
            
            # 创建相册管理器实例
            manager = AlbumManager()
            
            # 测试获取PhotosAlbum实例
            photos_album = manager._get_photos_album()
            
            # 验证PhotosAlbum被正确初始化
            mock_photos_album_class.assert_called_once_with('marked delete')
            print("✅ PhotosAlbum初始化参数正确")
            
            # 测试确保相册存在
            result = manager.ensure_album_exists()
            print(f"✅ 确保相册存在: {'成功' if result else '失败'}")
            
            # 测试添加照片
            test_uuid = "test-uuid-12345"
            add_result = manager.add_photo_to_album(test_uuid)
            print(f"✅ 添加照片: {'成功' if add_result else '失败'}")
            
            # 验证add方法被调用
            mock_photos_album.add.assert_called_once_with([test_uuid])
            print("✅ PhotosAlbum.add方法被正确调用")
            
            # 测试移除照片
            remove_result = manager.remove_photo_from_album(test_uuid)
            print(f"✅ 移除照片: {'成功' if remove_result else '失败'}")
            
            # 验证remove方法被调用
            mock_photos_album.remove.assert_called_once_with([test_uuid])
            print("✅ PhotosAlbum.remove方法被正确调用")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_initialization_function():
    """测试初始化函数"""
    print("\n🧪 测试初始化函数...")
    
    try:
        from photo_dedup.collector import initialize_photo_marking_system
        
        # 模拟相册管理器
        with patch('photo_dedup.album_manager.AlbumManager') as mock_manager_class:
            mock_manager = MagicMock()
            mock_manager_class.return_value = mock_manager
            mock_manager.ensure_album_exists.return_value = True
            
            # 调用初始化函数
            result = initialize_photo_marking_system()
            print(f"✅ 初始化函数: {'成功' if result else '失败'}")
            
            # 验证相册管理器被调用
            mock_manager.ensure_album_exists.assert_called_once()
            print("✅ 相册管理器被正确调用")
            
        return True
        
    except Exception as e:
        print(f"❌ 初始化函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 启动PhotosAlbum API修复测试")
    print("="*50)
    
    # 运行测试
    init_result = test_photosalbum_initialization()
    func_result = test_initialization_function()
    
    print("\n" + "="*50)
    print("📋 测试摘要")
    print("="*50)
    print(f"PhotosAlbum初始化测试: {'✅ 通过' if init_result else '❌ 失败'}")
    print(f"初始化函数测试: {'✅ 通过' if func_result else '❌ 失败'}")
    
    if init_result and func_result:
        print("\n🎉 所有测试通过！PhotosAlbum API修复成功。")
        print("\n📖 修复内容:")
        print("   1. ✅ PhotosAlbum初始化使用正确的name参数")
        print("   2. ✅ 使用PhotosAlbum.add()和PhotosAlbum.remove()方法")
        print("   3. ✅ 简化了相册存在检查逻辑")
        print("   4. ✅ 保持错误容错机制")
    else:
        print("\n❌ 部分测试失败，需要进一步检查。")
    
    print("="*50)
    
    return init_result and func_result


if __name__ == "__main__":
    result = main()
    sys.exit(0 if result else 1)
