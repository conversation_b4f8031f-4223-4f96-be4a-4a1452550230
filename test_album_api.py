#!/usr/bin/env python3
"""
测试系统相册API的正确用法
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src-python', 'python'))

def test_osxphotos_api():
    """测试osxphotos API的正确用法"""
    print("🧪 测试osxphotos API...")
    
    try:
        # 导入osxphotos
        import osxphotos
        from osxphotos.photosalbum import PhotosAlbum
        
        print("✅ osxphotos导入成功")
        
        # 测试PhotosAlbum的正确初始化方式
        try:
            # 方法1：直接使用相册名称
            album = PhotosAlbum("marked delete")
            print("✅ PhotosAlbum初始化成功（方法1：直接使用相册名称）")
            
            # 检查可用的方法
            print(f"📋 PhotosAlbum可用方法: {[method for method in dir(album) if not method.startswith('_')]}")
            
            # 测试添加照片的方法
            test_uuid = "DDDCEB2B-B8BA-4FEA-B95A-B08BDF31D469"
            
            # 检查是否有add方法
            if hasattr(album, 'add'):
                print("✅ PhotosAlbum有add方法")
                try:
                    # 尝试添加照片
                    result = album.add([test_uuid])
                    print(f"📸 添加照片结果: {result}")
                except Exception as e:
                    print(f"⚠️ 添加照片失败: {e}")
            else:
                print("❌ PhotosAlbum没有add方法")
                
            # 检查是否有其他相关方法
            relevant_methods = [method for method in dir(album) if any(keyword in method.lower() for keyword in ['add', 'create', 'album', 'photo'])]
            print(f"📋 相关方法: {relevant_methods}")
            
        except Exception as e:
            print(f"❌ PhotosAlbum初始化失败: {e}")
            
        # 方法2：尝试使用PhotosDB
        try:
            photosdb = osxphotos.PhotosDB()
            print("✅ PhotosDB初始化成功")
            
            # 检查PhotosDB的相册相关方法
            album_methods = [method for method in dir(photosdb) if 'album' in method.lower()]
            print(f"📋 PhotosDB相册相关方法: {album_methods}")
            
        except Exception as e:
            print(f"❌ PhotosDB初始化失败: {e}")
            
    except ImportError as e:
        print(f"❌ osxphotos导入失败: {e}")
        print("💡 可能需要安装osxphotos: pip install osxphotos")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False
        
    return True

def test_alternative_approaches():
    """测试替代方案"""
    print("\n🧪 测试替代方案...")
    
    try:
        import osxphotos
        
        # 方法1：使用AppleScript（通过osascript）
        print("📝 方法1：使用AppleScript")
        import subprocess
        
        # 创建相册的AppleScript
        create_album_script = '''
        tell application "Photos"
            try
                set albumName to "marked delete"
                set folderName to "Photo Duplication Remove"
                
                -- 检查文件夹是否存在，不存在则创建
                try
                    set targetFolder to folder folderName
                on error
                    set targetFolder to make new folder with properties {name:folderName}
                end try
                
                -- 检查相册是否存在，不存在则创建
                try
                    set targetAlbum to album albumName of targetFolder
                on error
                    set targetAlbum to make new album with properties {name:albumName} at targetFolder
                end try
                
                return "success"
            on error errorMessage
                return "error: " & errorMessage
            end try
        end tell
        '''
        
        try:
            result = subprocess.run(['osascript', '-e', create_album_script], 
                                  capture_output=True, text=True, timeout=10)
            print(f"   AppleScript结果: {result.stdout.strip()}")
            if result.returncode == 0:
                print("   ✅ AppleScript创建相册成功")
            else:
                print(f"   ❌ AppleScript失败: {result.stderr}")
        except Exception as e:
            print(f"   ❌ AppleScript执行失败: {e}")
            
        # 方法2：检查osxphotos的其他API
        print("\n📝 方法2：检查osxphotos的其他API")
        try:
            photosdb = osxphotos.PhotosDB()
            
            # 获取所有相册
            albums = photosdb.albums
            print(f"   📚 找到 {len(albums)} 个相册")
            
            # 查找目标相册
            target_album = None
            for album in albums:
                if album.title == "marked delete":
                    target_album = album
                    break
                    
            if target_album:
                print(f"   ✅ 找到目标相册: {target_album.title}")
                print(f"   📸 相册中有 {len(target_album.photos)} 张照片")
            else:
                print("   ⚠️ 未找到目标相册")
                
        except Exception as e:
            print(f"   ❌ osxphotos API测试失败: {e}")
            
    except Exception as e:
        print(f"❌ 替代方案测试失败: {e}")
        return False
        
    return True

def main():
    """主函数"""
    print("🚀 启动系统相册API测试")
    print("="*60)
    
    # 测试osxphotos API
    api_result = test_osxphotos_api()
    
    # 测试替代方案
    alt_result = test_alternative_approaches()
    
    print("\n" + "="*60)
    print("📋 测试摘要")
    print("="*60)
    print(f"osxphotos API测试: {'✅ 通过' if api_result else '❌ 失败'}")
    print(f"替代方案测试: {'✅ 通过' if alt_result else '❌ 失败'}")
    
    if api_result or alt_result:
        print("\n💡 建议:")
        print("   1. 如果osxphotos API可用，使用正确的方法")
        print("   2. 如果API不可用，考虑使用AppleScript")
        print("   3. 作为最后手段，可以只使用数据库标记")
    else:
        print("\n❌ 所有方法都失败，建议只使用数据库标记")
    
    print("="*60)
    
    return api_result or alt_result

if __name__ == "__main__":
    result = main()
    sys.exit(0 if result else 1)
