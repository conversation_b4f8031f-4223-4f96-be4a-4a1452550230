#!/usr/bin/env python3
"""
详细测试PhotosAlbum API的正确用法
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src-python', 'python'))

def test_photosalbum_detailed():
    """详细测试PhotosAlbum API"""
    print("🧪 详细测试PhotosAlbum API...")

    try:
        import osxphotos
        from osxphotos.photosalbum import PhotosAlbum

        # 1. 创建PhotosDB实例来获取照片对象
        photosdb = osxphotos.PhotosDB()
        photos_list = photosdb.photos()  # 调用方法获取照片列表
        print(f"✅ PhotosDB初始化成功，找到 {len(photos_list)} 张照片")

        # 2. 找一张测试照片
        test_uuid = "DDDCEB2B-B8BA-4FEA-B95A-B08BDF31D469"
        test_photo = None

        for photo in photos_list:
            if photo.uuid == test_uuid:
                test_photo = photo
                break

        if test_photo:
            print(f"✅ 找到测试照片: {test_photo.filename}")
        else:
            # 使用第一张照片作为测试
            if photos_list:
                test_photo = photos_list[0]
                test_uuid = test_photo.uuid
                print(f"✅ 使用第一张照片作为测试: {test_photo.filename} ({test_uuid})")
            else:
                print("❌ 没有找到任何照片")
                return False

        # 3. 测试PhotosAlbum的正确用法
        try:
            # 创建或获取相册
            album = PhotosAlbum("marked delete")
            print("✅ PhotosAlbum初始化成功")

            # 检查相册是否已存在
            print(f"📚 相册名称: {album.name}")
            try:
                album_photos = album.photos()  # 调用方法
                print(f"📸 相册中当前照片数: {len(album_photos)}")
            except Exception as e:
                print(f"⚠️ 获取相册照片失败: {e}")
                album_photos = []

            # 4. 测试添加照片 - 使用Photo对象而不是UUID字符串
            try:
                print(f"📸 尝试添加照片对象到相册...")
                result = album.add([test_photo])  # 传入Photo对象而不是UUID字符串
                print(f"✅ 添加照片成功: {result}")

                # 检查相册中的照片数量
                try:
                    updated_photos = album.photos()
                    print(f"📸 添加后相册中照片数: {len(updated_photos)}")

                    # 验证照片是否真的在相册中
                    photo_uuids = [p.uuid for p in updated_photos]
                except Exception as e:
                    print(f"⚠️ 获取更新后照片列表失败: {e}")
                    photo_uuids = []
                if test_uuid in photo_uuids:
                    print(f"✅ 照片 {test_uuid} 确实在相册中")
                else:
                    print(f"⚠️ 照片 {test_uuid} 不在相册中")

            except Exception as e:
                print(f"❌ 添加照片失败: {e}")
                print(f"📋 错误类型: {type(e)}")

                # 尝试其他方法
                try:
                    print("🔄 尝试使用extend方法...")
                    album.extend([test_photo])
                    print("✅ extend方法成功")
                except Exception as e2:
                    print(f"❌ extend方法也失败: {e2}")

        except Exception as e:
            print(f"❌ PhotosAlbum操作失败: {e}")
            return False

        # 5. 测试相册创建（如果不存在）
        try:
            print("\n🔧 测试相册创建...")

            # 尝试创建文件夹结构
            folder_album = PhotosAlbum("Photo Duplication Remove/marked delete")
            print("✅ 文件夹结构相册创建成功")

        except Exception as e:
            print(f"⚠️ 文件夹结构相册创建失败: {e}")

        return True

    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_applescript_fixed():
    """测试修复后的AppleScript"""
    print("\n🧪 测试修复后的AppleScript...")

    try:
        import subprocess

        # 简化的AppleScript - 只创建相册
        create_album_script = '''
tell application "Photos"
    activate
    try
        make new album with properties {name:"marked delete"}
        return "success"
    on error errorMessage
        return "error: " & errorMessage
    end try
end tell
        '''

        try:
            result = subprocess.run(['osascript', '-e', create_album_script],
                                  capture_output=True, text=True, timeout=15)
            print(f"📝 AppleScript结果: {result.stdout.strip()}")
            if result.returncode == 0 and "success" in result.stdout:
                print("✅ AppleScript创建相册成功")
                return True
            else:
                print(f"❌ AppleScript失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ AppleScript执行失败: {e}")
            return False

    except Exception as e:
        print(f"❌ AppleScript测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 启动详细的系统相册API测试")
    print("="*60)

    # 测试PhotosAlbum详细用法
    api_result = test_photosalbum_detailed()

    # 测试修复后的AppleScript
    script_result = test_applescript_fixed()

    print("\n" + "="*60)
    print("📋 测试摘要")
    print("="*60)
    print(f"PhotosAlbum详细测试: {'✅ 通过' if api_result else '❌ 失败'}")
    print(f"AppleScript测试: {'✅ 通过' if script_result else '❌ 失败'}")

    if api_result:
        print("\n💡 PhotosAlbum使用要点:")
        print("   1. ✅ 需要传入Photo对象而不是UUID字符串")
        print("   2. ✅ 使用PhotosDB获取Photo对象")
        print("   3. ✅ 相册会自动创建如果不存在")

    if script_result:
        print("\n💡 AppleScript使用要点:")
        print("   1. ✅ 可以创建文件夹和相册结构")
        print("   2. ✅ 作为PhotosAlbum的备选方案")

    print("="*60)

    return api_result or script_result

if __name__ == "__main__":
    result = main()
    sys.exit(0 if result else 1)
